# @ngnair/auth-integration-package

Complete authentication module package with Rails-compatible AES-256-GCM cookie decryption and JWT processing for NestJS applications.

## 🚀 Features

- **Rails-Compatible Decryption**: AES-256-GCM cookie decryption matching Rails MessageEncryptor format
- **Local JWT Processing**: `/auth/me` endpoint processes JWTs locally without external API calls
- **External Service Integration**: `/auth/users` endpoints forward requests to external authentication service
- **TypeScript Support**: Full TypeScript definitions and type safety
- **Flexible Configuration**: Environment variables or programmatic configuration
- **Authentication Guards**: Built-in guards for route protection
- **Comprehensive Logging**: Detailed logging for debugging and monitoring

## 📦 Installation

```bash
npm install @ngnair/auth-integration-package
```

### Required Dependencies

The package requires these peer dependencies:

```bash
npm install @nestjs/common @nestjs/core @nestjs/axios @nestjs/swagger
npm install axios class-transformer class-validator jwks-client
```

## 🔧 Quick Setup

### 1. Environment Variables

Create or update your `.env` file:

```env
# Required: AES-256 encryption key (64-character hex string)
ACCESS_TOKEN_ENCRYPTION_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456

# Required: External authentication service URL
EXTERNAL_AUTH_SERVICE_URL=https://auth.example.com/api

# Optional: JWKS URI for JWT verification
JWKS_URI=https://auth.example.com/.well-known/jwks.json

# Optional: Cookie names (defaults shown)
ACCESS_TOKEN_COOKIE_NAME=access_token
REFRESH_TOKEN_COOKIE_NAME=refresh_token

# Optional: Request timeout in milliseconds
AUTH_REQUEST_TIMEOUT=10000

# Optional: Enable debug logging
AUTH_DEBUG_LOGGING=false

# Optional: JWT verification settings
JWT_ISSUER=https://auth.example.com
JWT_AUDIENCE=your-app
JWT_SKIP_VERIFICATION=false
```

### 2. Import the Module

In your `app.module.ts`:

```typescript
import { Module } from '@nestjs/common';
import { AuthModule } from '@ngnair/auth-integration-package';

@Module({
  imports: [
    // Option 1: Use environment variables (recommended)
    AuthModule.forRoot(),
    
    // Option 2: Manual configuration
    // AuthModule.register({
    //   encryptionKey: 'your-64-char-hex-key',
    //   externalAuthServiceUrl: 'https://auth.example.com/api',
    //   cookieNames: {
    //     accessToken: 'access_token',
    //     refreshToken: 'refresh_token',
    //   },
    // }),
  ],
})
export class AppModule {}
```

### 3. Enable Cookie Parsing

In your `main.ts`:

```typescript
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as cookieParser from 'cookie-parser';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Enable cookie parsing
  app.use(cookieParser());
  
  await app.listen(3000);
}
bootstrap();
```

## 📚 API Endpoints

The package provides three main endpoints:

### `/auth/me` - Get Current User (Local JWT Processing)

- **Method**: GET
- **Description**: Authenticates user using encrypted cookies, decrypts access token, verifies JWT locally
- **External API Calls**: None (processes JWT locally)
- **Response**: User information from JWT payload

```typescript
// Example response
{
  "id": "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
  "email": "<EMAIL>",
  "username": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "role": "admin",
  "permissions": [],
  "createdAt": "2025-08-29T08:09:09.000Z",
  "updatedAt": "2025-08-31T08:18:04.354Z"
}
```

### `/auth/users` - Get All Users (External Service)

- **Method**: GET
- **Description**: Forwards request to external authentication service
- **External API Calls**: Yes (proxies to external service)
- **Authentication**: Required (uses AuthGuard)

### `/auth/users/:id` - Get User by ID (External Service)

- **Method**: GET
- **Description**: Forwards request to external authentication service for specific user
- **External API Calls**: Yes (proxies to external service)
- **Authentication**: Required (uses AuthGuard)

## 🔐 Authentication Flow

The package implements the exact 4-step JWT authentication sequence that matches the Ruby implementation:

### Step 1: Extract and URL Decode
- Extracts `access_token` from HTTP cookies
- URL decodes the cookie value to handle special characters

### Step 2: Base64 Decode
- Base64 decodes to get Rails MessageEncryptor format
- Results in format: `encrypted_data--iv--auth_tag`

### Step 3: AES-256-GCM Decryption
- Parses the three components (encrypted data, IV, auth tag)
- Decrypts using AES-256-GCM with the configured encryption key
- Extracts the JWT token string

### Step 4: JWT Verification and Payload Extraction
- Decodes the JWT payload without signature verification (configurable)
- Returns **ONLY actual JWT fields** (no computed or hardcoded fields)
- Validates required fields based on Ruby JWT structure

### JWT Payload Structure

The package returns JWT payloads that match the Ruby implementation exactly:

```json
{
  "iss": "https://ng-auth-dev.dev1.ngnair.com",
  "sub": "f7b98e6f-95af-4d54-9c53-312ada49ba6e",
  "aud": "ngnair",
  "exp": 1756774160,
  "iat": 1756687760,
  "jti": "1af073f4-71e4-4aca-99b9-f41f92770a0a",
  "sid": "sess_35ee063223007077",
  "azp": "webapp",
  "ent_set": "de2e18a49c8b",
  "perm_v": 0,
  "amr": ["pwd"],
  "auth_time": 1756687760,
  "email": "<EMAIL>"
}
```

**Key Points:**
- No hardcoded `role` or `permissions` fields
- No computed fields like `createdAt`, `updatedAt`, or `expiresAt`
- Only actual fields from the decrypted JWT payload
- `email` field is optional and may not be present in all JWTs

## 🛡️ Using Authentication Guards

### Protect Routes with AuthGuard

```typescript
import { Controller, Get, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@ngnair/auth-integration-package';

@Controller('protected')
export class ProtectedController {
  @UseGuards(AuthGuard)
  @Get()
  getProtectedData() {
    return { message: 'This is protected data' };
  }
}
```

### Optional Authentication with OptionalAuthGuard

```typescript
import { Controller, Get, UseGuards, Req } from '@nestjs/common';
import { OptionalAuthGuard } from '@ngnair/auth-integration-package';

@Controller('optional')
export class OptionalController {
  @UseGuards(OptionalAuthGuard)
  @Get()
  getData(@Req() request: any) {
    const user = request.user; // Will be undefined if not authenticated
    return {
      message: user ? `Hello ${user.email}` : 'Hello anonymous user'
    };
  }
}
```

## 🔧 Advanced Configuration

### Programmatic Configuration

```typescript
import { AuthModule, AuthConfig } from '@ngnair/auth-integration-package';

const authConfig: AuthConfig = {
  encryptionKey: process.env.ACCESS_TOKEN_ENCRYPTION_KEY,
  externalAuthServiceUrl: process.env.EXTERNAL_AUTH_SERVICE_URL,
  jwksUri: process.env.JWKS_URI,
  cookieNames: {
    accessToken: 'custom_access_token',
    refreshToken: 'custom_refresh_token',
  },
  requestTimeout: 15000,
  enableDebugLogging: true,
  jwt: {
    issuer: 'https://auth.example.com',
    audience: 'your-app',
    skipVerification: false,
  },
};

@Module({
  imports: [AuthModule.register(authConfig)],
})
export class AppModule {}
```

### Async Configuration

```typescript
import { ConfigService } from '@nestjs/config';
import { AuthModule } from '@ngnair/auth-integration-package';

@Module({
  imports: [
    AuthModule.registerAsync({
      useFactory: (configService: ConfigService) => ({
        encryptionKey: configService.get('ACCESS_TOKEN_ENCRYPTION_KEY'),
        externalAuthServiceUrl: configService.get('EXTERNAL_AUTH_SERVICE_URL'),
        jwksUri: configService.get('JWKS_URI'),
        cookieNames: {
          accessToken: configService.get('ACCESS_TOKEN_COOKIE_NAME', 'access_token'),
          refreshToken: configService.get('REFRESH_TOKEN_COOKIE_NAME', 'refresh_token'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
})
export class AppModule {}
```

## 📁 Directory Structure

When integrating this package, your project structure should look like this:

```
your-nestjs-app/
├── src/
│   ├── app.module.ts          # Import AuthModule here
│   ├── main.ts                # Enable cookie parsing here
│   ├── controllers/           # Your application controllers
│   ├── services/              # Your application services
│   └── ...
├── .env                       # Environment variables
├── package.json               # Add package dependency
└── ...
```

## 🔍 Key Differences: Local vs External Processing

### `/auth/me` Endpoint (Local Processing)
- ✅ Extracts `access_token` from cookies
- ✅ Decrypts cookie using Rails-compatible AES-256-GCM
- ✅ Verifies JWT locally using JWKS (when available)
- ✅ Returns user data from JWT payload
- ❌ **NO external API calls**

### `/auth/users` Endpoints (External Processing)
- ✅ Extracts encrypted `access_token` from cookies
- ✅ Forwards encrypted cookie to external auth service
- ✅ Acts as proxy to external authentication service
- ✅ **Makes external API calls**

## 🐛 Troubleshooting

### Common Issues

1. **"Missing access token" Error**
   - Ensure cookies are being sent with requests
   - Verify cookie names match configuration
   - Check that `cookieParser()` middleware is enabled

2. **"Failed to decrypt authentication token" Error**
   - Verify `ACCESS_TOKEN_ENCRYPTION_KEY` is correct 64-character hex string
   - Ensure the encryption key matches the one used to encrypt cookies
   - Check that cookies are in Rails MessageEncryptor format

3. **"Authentication failed" Error**
   - Check JWT verification settings
   - Verify JWKS URI is accessible
   - Consider enabling `JWT_SKIP_VERIFICATION` for development

### Debug Logging

Enable debug logging to see detailed authentication flow:

```env
AUTH_DEBUG_LOGGING=true
```

This will show detailed logs including:
- Cookie parsing
- Decryption process
- JWT verification
- User extraction

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📞 Support

For issues and questions:
- GitHub Issues: [Create an issue](https://github.com/ngnair/auth-integration-package/issues)
- Documentation: [Full documentation](https://github.com/ngnair/auth-integration-package/wiki)

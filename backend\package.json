{"name": "nest-template", "version": "0.1.0", "description": "NestJS API project template crafted for Docker environments", "author": "Saluki", "license": "MIT", "private": true, "engines": {"node": ">=20.0.0"}, "scripts": {"clean": "rimraf ./dist", "start": "node dist/main.js", "dev": "env-cmd -f .env nodemon", "build": "prisma generate --schema=./prisma/schema.prisma && npm run clean && tsc", "test": "env-cmd -f .env jest", "test:watch": "env-cmd -f .env jest --watch", "test:coverage": "env-cmd -f .env jest --coverage", "test:e2e": "env-cmd -f .env jest --config jest.config.js --testPathPattern=e2e", "test:unit": "env-cmd -f .env jest --config jest.config.js --testPathPattern=spec.ts", "lint": "eslint -c .eslintrc.js --ext .ts 'src/**/*.ts'", "seed:auth": "env-cmd -f .env ts-node src/scripts/seed-auth.ts", "seed:users": "env-cmd -f .env ts-node src/scripts/seed-users.ts", "seed:comprehensive": "env-cmd -f .env ts-node scripts/comprehensive-seed.ts", "validate:db": "env-cmd -f .env ts-node scripts/validate-db-config.ts", "migrate:generate": "prisma migrate dev --create-only --schema=./prisma/schema.prisma", "migrate:up": "prisma migrate dev --schema=./prisma/schema.prisma", "migrate:deploy": "prisma migrate deploy --schema=./prisma/schema.prisma", "migrate:status": "prisma migrate status --schema=./prisma/schema.prisma", "prisma:seed": "env-cmd -f .env ts-node src/prisma/seed.ts"}, "dependencies": {"@apollo/server": "^4.12.2", "@as-integrations/fastify": "^2.1.1", "@aws-sdk/client-s3": "3.400.0", "@aws-sdk/s3-request-presigner": "3.400.0", "@fastify/cookie": "^10.0.1", "@fastify/cors": "^11.0.1", "@fastify/helmet": "^11.1.1", "@fastify/multipart": "^9.0.3", "@fastify/static": "^8.1.1", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@nestjs/apollo": "^13.1.0", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.15", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.15", "@nestjs/graphql": "^13.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-fastify": "^11.0.15", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^11.0.0", "@nestjs/testing": "^11.0.15", "@prisma/client": "^6.11.1", "@smithy/node-http-handler": "^2.2.0", "@types/express": "^5.0.3", "@types/passport-jwt": "^4.0.1", "apollo-server-express": "^3.13.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "fastify": "^4.29.1", "fs-extra": "^11.3.0", "glob": "^10.3.10", "graphql": "^16.11.0", "graphql-ws": "^5.14.3", "ioredis": "^5.6.0", "joi": "^17.11.0", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "jwks-client": "^2.0.5", "node-fetch": "^3.3.2", "passport": "^0.7.0", "passport-http": "^0.3.0", "passport-jwt": "^4.0.1", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.1.12", "rxjs": "^7.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.0.0", "@types/passport-http": "^0.3.11", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "env-cmd": "^10.1.0", "eslint": "^8.56.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prefer-arrow": "^1.2.3", "husky": "^8.0.3", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^6.11.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}
-- CreateEnum
CREATE TYPE "PaymentTokenStatus" AS ENUM ('ACTIVE', 'EXPIRED', 'REVOKED', 'SUSPENDED');

-- CreateEnum
CREATE TYPE "PaymentTokenType" AS ENUM ('CARD', 'BANK_ACCOUNT', 'DIGITAL_WALLET', 'TAP_TO_PAY');

-- CreateEnum
CREATE TYPE "PaymentProvider" AS ENUM ('ELAVON', 'TSEP', 'NEARPAY', 'STRIPE', 'SQUARE', 'OTHER');

-- CreateTable
CREATE TABLE "payment_tokens" (
    "id" TEXT NOT NULL,
    "customerId" TEXT NOT NULL,
    "tokenHash" TEXT NOT NULL,
    "externalTokenId" TEXT,
    "paymentProvider" "PaymentProvider" NOT NULL,
    "tokenType" "PaymentTokenType" NOT NULL,
    "status" "PaymentTokenStatus" NOT NULL DEFAULT 'ACTIVE',
    "maskedInfo" TEXT,
    "paymentBrand" TEXT,
    "expiresAt" TIMESTAMP(3),
    "elavonToken" TEXT,
    "elavonTransactionId" TEXT,
    "elavonReferenceId" TEXT,
    "elavonCardType" TEXT,
    "elavonApprovalCode" TEXT,
    "tsepToken" TEXT,
    "tsepTransactionId" TEXT,
    "tsepTransactionKey" TEXT,
    "tsepDeviceId" TEXT,
    "tsepCardType" TEXT,
    "tsepResponseCode" TEXT,
    "transactionAmount" DOUBLE PRECISION,
    "currency" TEXT,
    "customerName" TEXT,
    "customerEmail" TEXT,
    "rawWebhookData" JSONB,
    "providerMetadata" JSONB,
    "financeId" TEXT,
    "accountId" TEXT,
    "createdByIp" TEXT,
    "lastUsedAt" TIMESTAMP(3),
    "usageCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "payment_tokens_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "payment_tokens_tokenHash_key" ON "payment_tokens"("tokenHash");

-- CreateIndex
CREATE INDEX "payment_tokens_customerId_status_idx" ON "payment_tokens"("customerId", "status");

-- CreateIndex
CREATE INDEX "payment_tokens_tokenHash_idx" ON "payment_tokens"("tokenHash");

-- CreateIndex
CREATE INDEX "payment_tokens_externalTokenId_idx" ON "payment_tokens"("externalTokenId");

-- CreateIndex
CREATE INDEX "payment_tokens_paymentProvider_idx" ON "payment_tokens"("paymentProvider");

-- CreateIndex
CREATE INDEX "payment_tokens_elavonToken_idx" ON "payment_tokens"("elavonToken");

-- CreateIndex
CREATE INDEX "payment_tokens_tsepToken_idx" ON "payment_tokens"("tsepToken");

-- CreateIndex
CREATE INDEX "payment_tokens_customerEmail_idx" ON "payment_tokens"("customerEmail");

-- CreateIndex
CREATE INDEX "payment_tokens_createdAt_idx" ON "payment_tokens"("createdAt");

-- AddForeignKey
ALTER TABLE "payment_tokens" ADD CONSTRAINT "payment_tokens_customerId_fkey" FOREIGN KEY ("customerId") REFERENCES "customers"("id") ON DELETE CASCADE ON UPDATE CASCADE;

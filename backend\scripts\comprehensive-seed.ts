#!/usr/bin/env ts-node

/**
 * Comprehensive Database Seeding Script
 * 
 * This script generates realistic test data for the customer microservice including:
 * - 100+ customers with varied profiles (individual, business, enterprise)
 * - Multiple addresses per customer
 * - Payment methods with realistic token data
 * - Provider-specific payment data (Elavon, TSEP, etc.)
 * - Realistic distribution of customer statuses and verification states
 * 
 * Usage:
 *   npm run seed:comprehensive
 *   or
 *   npx ts-node scripts/comprehensive-seed.ts
 */

import { PrismaClient, CustomerStatus, CustomerType, AddressType, PaymentTokenStatus, PaymentTokenType, PaymentProvider } from '@prisma/client';
import { faker } from '@faker-js/faker';
import * as crypto from 'crypto';

const prisma = new PrismaClient();

// Configuration
const CONFIG = {
  CUSTOMERS_TO_CREATE: 150,
  MIN_ADDRESSES_PER_CUSTOMER: 1,
  MAX_ADDRESSES_PER_CUSTOMER: 3,
  MIN_PAYMENT_METHODS_PER_CUSTOMER: 1,
  MAX_PAYMENT_METHODS_PER_CUSTOMER: 4,
  BUSINESS_CUSTOMER_PROBABILITY: 0.3,
  PREMIUM_CUSTOMER_PROBABILITY: 0.2,
  HIGH_VALUE_CUSTOMER_PROBABILITY: 0.1,
};

// Helper functions
function generateTokenHash(tokenData: string): string {
  return crypto.createHash('sha256').update(tokenData).digest('hex');
}

function generateFakeCardNumber(brand: string): string {
  const prefixes: { [key: string]: string } = {
    'Visa': '4',
    'Mastercard': '5',
    'American Express': '3',
    'Discover': '6'
  };

  const prefix = prefixes[brand] || '4';
  let cardNumber = prefix;

  const remainingLength = brand === 'American Express' ? 14 : 15;
  for (let i = 0; i < remainingLength; i++) {
    cardNumber += Math.floor(Math.random() * 10).toString();
  }

  return cardNumber;
}

function maskCardNumber(cardNumber: string): string {
  if (cardNumber.length === 15) {
    return `**** ****** *${cardNumber.slice(-4)}`;
  }
  return `**** **** **** ${cardNumber.slice(-4)}`;
}

function generateRealisticEmail(firstName: string, lastName: string, companyName?: string | null): string {
  if (companyName && faker.datatype.boolean({ probability: 0.7 })) {
    const domain = companyName.toLowerCase()
      .replace(/[^a-z0-9]/g, '')
      .substring(0, 10) + '.com';
    return `${firstName.toLowerCase()}.${lastName.toLowerCase()}@${domain}`;
  }
  return faker.internet.email({ firstName, lastName }).toLowerCase();
}

async function clearExistingData() {
  console.log('🧹 Clearing existing data...');
  
  await prisma.paymentToken.deleteMany();
  await prisma.auditLog.deleteMany();
  await prisma.customerPreference.deleteMany();
  await prisma.contact.deleteMany();
  await prisma.address.deleteMany();
  await prisma.customer.deleteMany();
  
  console.log('✅ Existing data cleared');
}

async function generateCustomerData(index: number) {
  // Determine customer type and business status
  const isBusinessCustomer = faker.datatype.boolean({ probability: CONFIG.BUSINESS_CUSTOMER_PROBABILITY });
  const customerType = isBusinessCustomer ? 
    faker.helpers.arrayElement([CustomerType.BUSINESS, CustomerType.ENTERPRISE]) : 
    CustomerType.INDIVIDUAL;
  
  // Basic customer information
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  const companyName = isBusinessCustomer ? faker.company.name() : null;
  const email = generateRealisticEmail(firstName, lastName, companyName);
  const phone = faker.phone.number({ style: 'national' });
  const dateOfBirth = faker.date.birthdate({ min: 18, max: 80, mode: 'age' });
  
  // Generate realistic status distribution
  const status = faker.helpers.weightedArrayElement([
    { weight: 70, value: CustomerStatus.ACTIVE },
    { weight: 15, value: CustomerStatus.INACTIVE },
    { weight: 10, value: CustomerStatus.PENDING_VERIFICATION },
    { weight: 5, value: CustomerStatus.SUSPENDED }
  ]);

  // Verification flags based on status
  const isEmailVerified = status === CustomerStatus.ACTIVE ? 
    faker.datatype.boolean({ probability: 0.9 }) : 
    faker.datatype.boolean({ probability: 0.3 });
  const isPhoneVerified = status === CustomerStatus.ACTIVE ? 
    faker.datatype.boolean({ probability: 0.8 }) : 
    faker.datatype.boolean({ probability: 0.2 });
  const isKycVerified = status === CustomerStatus.ACTIVE ? 
    faker.datatype.boolean({ probability: 0.7 }) : false;

  // Business-specific data
  const taxId = isBusinessCustomer ? faker.string.numeric(9) : null;
  const businessTypes = ['Technology', 'Healthcare', 'Finance', 'Retail', 'Manufacturing', 'Consulting', 'Real Estate', 'Education'];
  const businessType = isBusinessCustomer ? faker.helpers.arrayElement(businessTypes) : null;

  // Generate tags
  const tags = [];
  if (status === CustomerStatus.ACTIVE) tags.push('active');
  if (isKycVerified) tags.push('verified');
  if (isBusinessCustomer) tags.push('business');
  if (customerType === CustomerType.ENTERPRISE) tags.push('enterprise');
  if (faker.datatype.boolean({ probability: CONFIG.PREMIUM_CUSTOMER_PROBABILITY })) tags.push('premium');
  if (faker.datatype.boolean({ probability: CONFIG.HIGH_VALUE_CUSTOMER_PROBABILITY })) tags.push('high-value');

  const notes = faker.datatype.boolean({ probability: 0.6 }) ? faker.lorem.sentence() : null;

  return {
    firstName,
    lastName,
    email,
    phone,
    dateOfBirth,
    companyName,
    taxId,
    businessType,
    status,
    type: customerType,
    isEmailVerified,
    isPhoneVerified,
    isKycVerified,
    tags,
    notes,
  };
}

async function generateAddresses(customerId: string) {
  const countries = ['US', 'CA', 'GB', 'AU', 'DE', 'FR', 'JP', 'BR'];
  const addressTypes = [AddressType.HOME, AddressType.WORK, AddressType.BILLING, AddressType.SHIPPING];
  
  const numAddresses = faker.number.int({ 
    min: CONFIG.MIN_ADDRESSES_PER_CUSTOMER, 
    max: CONFIG.MAX_ADDRESSES_PER_CUSTOMER 
  });
  
  const addresses = [];
  
  for (let i = 0; i < numAddresses; i++) {
    const country = faker.helpers.arrayElement(countries);
    const addressType = i === 0 ? AddressType.HOME : faker.helpers.arrayElement(addressTypes);
    
    addresses.push({
      customerId,
      type: addressType,
      label: addressType === AddressType.HOME ? 'Home' : 
             addressType === AddressType.WORK ? 'Office' :
             addressType === AddressType.BILLING ? 'Billing' : 'Shipping',
      street1: faker.location.streetAddress(),
      street2: faker.datatype.boolean({ probability: 0.3 }) ? faker.location.secondaryAddress() : null,
      city: faker.location.city(),
      state: country === 'US' ? faker.location.state({ abbreviated: true }) : faker.location.state(),
      postalCode: country === 'US' ? faker.location.zipCode() : faker.location.zipCode(),
      country: country,
      isDefault: i === 0,
      isVerified: faker.datatype.boolean({ probability: 0.7 }),
      latitude: faker.datatype.boolean({ probability: 0.4 }) ? faker.location.latitude() : null,
      longitude: faker.datatype.boolean({ probability: 0.4 }) ? faker.location.longitude() : null,
    });
  }
  
  return addresses;
}

async function generatePaymentTokens(customerId: string, customerEmail: string, customerName: string, customerStatus: CustomerStatus) {
  // Only generate payment methods for active customers (mostly)
  if (customerStatus !== CustomerStatus.ACTIVE && !faker.datatype.boolean({ probability: 0.3 })) {
    return [];
  }

  const paymentProviders = [PaymentProvider.ELAVON, PaymentProvider.TSEP, PaymentProvider.NEARPAY, PaymentProvider.STRIPE];
  const paymentTokenTypes = [PaymentTokenType.CARD, PaymentTokenType.BANK_ACCOUNT, PaymentTokenType.DIGITAL_WALLET];
  const cardBrands = ['Visa', 'Mastercard', 'American Express', 'Discover'];
  
  const numPaymentMethods = faker.number.int({ 
    min: CONFIG.MIN_PAYMENT_METHODS_PER_CUSTOMER, 
    max: CONFIG.MAX_PAYMENT_METHODS_PER_CUSTOMER 
  });
  
  const paymentTokens = [];
  
  for (let i = 0; i < numPaymentMethods; i++) {
    const provider = faker.helpers.arrayElement(paymentProviders);
    const tokenType = faker.helpers.arrayElement(paymentTokenTypes);
    const cardBrand = tokenType === PaymentTokenType.CARD ? faker.helpers.arrayElement(cardBrands) : null;
    
    // Generate token data
    const rawToken = faker.string.alphanumeric(32);
    const tokenHash = generateTokenHash(rawToken);
    
    let maskedInfo = '';
    let expiresAt = null;
    
    if (tokenType === PaymentTokenType.CARD && cardBrand) {
      const cardNumber = generateFakeCardNumber(cardBrand);
      maskedInfo = maskCardNumber(cardNumber);
      expiresAt = faker.date.future({ years: 4 });
    } else if (tokenType === PaymentTokenType.BANK_ACCOUNT) {
      maskedInfo = `****${faker.string.numeric(4)}`;
    } else {
      maskedInfo = `${cardBrand || 'Digital'} Wallet`;
    }

    // Provider-specific data
    let providerData = {};
    
    if (provider === PaymentProvider.ELAVON) {
      providerData = {
        elavonToken: `ssl_${faker.string.alphanumeric(16)}`,
        elavonTransactionId: faker.string.numeric(10),
        elavonReferenceId: faker.string.alphanumeric(12),
        elavonCardType: cardBrand === 'Visa' ? 'VISA' : 
                       cardBrand === 'Mastercard' ? 'MAST' :
                       cardBrand === 'American Express' ? 'AMEX' : 'DISC',
        elavonApprovalCode: faker.string.alphanumeric(6),
      };
    } else if (provider === PaymentProvider.TSEP) {
      providerData = {
        tsepToken: `tsep_${faker.string.alphanumeric(20)}`,
        tsepTransactionId: faker.string.numeric(12),
        tsepTransactionKey: faker.string.alphanumeric(16),
        tsepDeviceId: faker.string.alphanumeric(8),
        tsepCardType: cardBrand === 'Visa' ? 'V' : 
                     cardBrand === 'Mastercard' ? 'M' :
                     cardBrand === 'American Express' ? 'A' : 'D',
        tsepResponseCode: '00',
      };
    }

    const transactionAmount = faker.number.float({ min: 10, max: 5000, fractionDigits: 2 });
    const financeId = faker.datatype.boolean({ probability: 0.6 }) ? faker.string.uuid() : null;
    const accountId = faker.datatype.boolean({ probability: 0.6 }) ? faker.string.uuid() : null;

    paymentTokens.push({
      customerId,
      tokenHash,
      externalTokenId: `ext_${faker.string.alphanumeric(16)}`,
      paymentProvider: provider,
      tokenType,
      status: faker.helpers.weightedArrayElement([
        { weight: 85, value: PaymentTokenStatus.ACTIVE },
        { weight: 10, value: PaymentTokenStatus.EXPIRED },
        { weight: 3, value: PaymentTokenStatus.SUSPENDED },
        { weight: 2, value: PaymentTokenStatus.REVOKED }
      ]),
      maskedInfo,
      paymentBrand: cardBrand,
      expiresAt,
      transactionAmount,
      currency: 'USD',
      customerName,
      customerEmail,
      financeId,
      accountId,
      createdByIp: faker.internet.ip(),
      lastUsedAt: faker.datatype.boolean({ probability: 0.7 }) ? faker.date.recent({ days: 30 }) : null,
      usageCount: faker.number.int({ min: 0, max: 50 }),
      rawWebhookData: {
        provider: provider,
        timestamp: new Date().toISOString(),
        amount: transactionAmount,
        currency: 'USD',
        status: 'success'
      },
      providerMetadata: {
        provider: provider,
        ...providerData
      },
      ...providerData
    });
  }
  
  return paymentTokens;
}

async function main() {
  console.log('🌱 Starting comprehensive database seeding...');
  console.log(`📊 Configuration: ${CONFIG.CUSTOMERS_TO_CREATE} customers with realistic data`);

  try {
    // Clear existing data
    await clearExistingData();

    // Generate customers with all related data
    console.log('👥 Creating customers with addresses and payment methods...');

    for (let i = 0; i < CONFIG.CUSTOMERS_TO_CREATE; i++) {
      // Generate customer data
      const customerData = await generateCustomerData(i);

      // Create customer
      const customer = await prisma.customer.create({
        data: customerData,
      });

      // Generate and create addresses
      const addresses = await generateAddresses(customer.id);
      await prisma.address.createMany({
        data: addresses,
      });

      // Generate and create payment tokens
      const paymentTokens = await generatePaymentTokens(
        customer.id,
        customer.email,
        `${customer.firstName} ${customer.lastName}`,
        customer.status
      );

      if (paymentTokens.length > 0) {
        await prisma.paymentToken.createMany({
          data: paymentTokens,
        });
      }

      // Progress indicator
      if ((i + 1) % 10 === 0) {
        console.log(`✅ Created ${i + 1}/${CONFIG.CUSTOMERS_TO_CREATE} customers...`);
      }
    }

    // Generate summary statistics
    console.log('\n🎉 Database seeding completed successfully!');
    await generateSummaryReport();

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  }
}

async function generateSummaryReport() {
  const totalCustomers = await prisma.customer.count();
  const totalAddresses = await prisma.address.count();
  const totalPaymentTokens = await prisma.paymentToken.count();

  const customersByStatus = await prisma.customer.groupBy({
    by: ['status'],
    _count: { status: true },
  });

  const customersByType = await prisma.customer.groupBy({
    by: ['type'],
    _count: { type: true },
  });

  const paymentTokensByProvider = await prisma.paymentToken.groupBy({
    by: ['paymentProvider'],
    _count: { paymentProvider: true },
  });

  const paymentTokensByType = await prisma.paymentToken.groupBy({
    by: ['tokenType'],
    _count: { tokenType: true },
  });

  console.log('\n📊 COMPREHENSIVE SEEDING SUMMARY:');
  console.log('=' .repeat(50));
  console.log(`👥 Total Customers: ${totalCustomers}`);
  console.log(`📍 Total Addresses: ${totalAddresses}`);
  console.log(`💳 Total Payment Tokens: ${totalPaymentTokens}`);
  console.log(`📈 Average Addresses per Customer: ${(totalAddresses / totalCustomers).toFixed(2)}`);
  console.log(`💰 Average Payment Methods per Customer: ${(totalPaymentTokens / totalCustomers).toFixed(2)}`);

  console.log('\n📈 Customer Status Distribution:');
  customersByStatus.forEach(item => {
    const percentage = ((item._count.status / totalCustomers) * 100).toFixed(1);
    console.log(`   ${item.status}: ${item._count.status} (${percentage}%)`);
  });

  console.log('\n🏢 Customer Type Distribution:');
  customersByType.forEach(item => {
    const percentage = ((item._count.type / totalCustomers) * 100).toFixed(1);
    console.log(`   ${item.type}: ${item._count.type} (${percentage}%)`);
  });

  console.log('\n💰 Payment Provider Distribution:');
  paymentTokensByProvider.forEach(item => {
    const percentage = ((item._count.paymentProvider / totalPaymentTokens) * 100).toFixed(1);
    console.log(`   ${item.paymentProvider}: ${item._count.paymentProvider} (${percentage}%)`);
  });

  console.log('\n💳 Payment Method Type Distribution:');
  paymentTokensByType.forEach(item => {
    const percentage = ((item._count.tokenType / totalPaymentTokens) * 100).toFixed(1);
    console.log(`   ${item.tokenType}: ${item._count.tokenType} (${percentage}%)`);
  });

  console.log('\n✨ Realistic test data generated successfully!');
  console.log('🚀 Ready for development and testing!');
  console.log('\n💡 Usage Examples:');
  console.log('   - Test customer search and filtering');
  console.log('   - Validate payment method integrations');
  console.log('   - Test different customer statuses and types');
  console.log('   - Verify address handling and validation');
  console.log('   - Test provider-specific payment flows');
}

// Execute the seeding script
main()
  .catch((e) => {
    console.error('❌ Fatal error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
    console.log('\n🔌 Database connection closed');
  });

#!/usr/bin/env ts-node

/**
 * Database Configuration Validation Script
 * 
 * This script validates that the database configuration is correct
 * and the seeding system will work with the current setup.
 * 
 * Usage:
 *   npm run validate:db
 *   or
 *   npx ts-node scripts/validate-db-config.ts
 */

import { PrismaClient } from '@prisma/client';
import * as crypto from 'crypto';

const prisma = new PrismaClient({
  log: ['error', 'warn'],
  errorFormat: 'pretty',
});

async function validateDatabaseConfiguration() {
  console.log('🔍 Database Configuration Validation');
  console.log('=' .repeat(50));
  
  try {
    // 1. Check environment variables
    console.log('\n📋 Environment Configuration:');
    const databaseUrl = process.env.DATABASE_URL;
    
    if (!databaseUrl) {
      console.error('❌ DATABASE_URL is not set in environment variables');
      return false;
    }
    
    // Mask sensitive information for display
    const maskedUrl = databaseUrl.replace(/:[^:@]*@/, ':***@');
    console.log(`   DATABASE_URL: ${maskedUrl}`);
    
    // Parse DATABASE_URL to extract components
    try {
      const url = new URL(databaseUrl);
      console.log(`   Protocol: ${url.protocol}`);
      console.log(`   Host: ${url.hostname}`);
      console.log(`   Port: ${url.port || 'default'}`);
      console.log(`   Database: ${url.pathname.slice(1)}`);
      console.log(`   Username: ${url.username}`);
    } catch (error) {
      console.error('❌ Invalid DATABASE_URL format');
      return false;
    }
    
    // 2. Test database connection
    console.log('\n🔗 Database Connection Test:');
    try {
      await prisma.$connect();
      console.log('   ✅ Connection successful');
      
      // Test basic query
      const result = await prisma.$queryRaw`SELECT 1 as test, NOW() as timestamp`;
      console.log('   ✅ Query execution successful');
      console.log(`   📅 Database time: ${result[0].timestamp}`);
      
    } catch (error) {
      console.error('   ❌ Connection failed:', error.message);
      return false;
    }
    
    // 3. Check database schema
    console.log('\n📊 Database Schema Validation:');
    try {
      // Check if required tables exist
      const tables = await prisma.$queryRaw`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_type = 'BASE TABLE'
        ORDER BY table_name
      `;
      
      const tableNames = tables.map(t => t.table_name);
      console.log(`   📋 Found ${tableNames.length} tables: ${tableNames.join(', ')}`);
      
      const requiredTables = ['customers', 'addresses', 'payment_tokens'];
      const missingTables = requiredTables.filter(table => !tableNames.includes(table));
      
      if (missingTables.length > 0) {
        console.error(`   ❌ Missing required tables: ${missingTables.join(', ')}`);
        console.error('   💡 Run migrations: npx prisma migrate deploy');
        return false;
      } else {
        console.log('   ✅ All required tables present');
      }
      
    } catch (error) {
      console.error('   ❌ Schema validation failed:', error.message);
      return false;
    }
    
    // 4. Test CRUD operations
    console.log('\n🧪 CRUD Operations Test:');
    try {
      // Create a test customer
      const testCustomer = await prisma.customer.create({
        data: {
          firstName: 'Test',
          lastName: 'Validation',
          email: `test-${Date.now()}@validation.com`,
          phone: '******-TEST',
          dateOfBirth: new Date('1990-01-01'),
          type: 'INDIVIDUAL',
          status: 'ACTIVE',
        },
      });
      console.log('   ✅ CREATE operation successful');
      
      // Read the customer
      const foundCustomer = await prisma.customer.findUnique({
        where: { id: testCustomer.id },
      });
      console.log('   ✅ READ operation successful');
      
      // Update the customer
      await prisma.customer.update({
        where: { id: testCustomer.id },
        data: { notes: 'Validation test completed' },
      });
      console.log('   ✅ UPDATE operation successful');
      
      // Delete the customer
      await prisma.customer.delete({
        where: { id: testCustomer.id },
      });
      console.log('   ✅ DELETE operation successful');
      
    } catch (error) {
      console.error('   ❌ CRUD operations failed:', error.message);
      return false;
    }
    
    // 5. Test seeding prerequisites
    console.log('\n🌱 Seeding Prerequisites Check:');
    try {
      // Check if we can clear data (permissions test)
      const customerCount = await prisma.customer.count();
      console.log(`   📊 Current customer count: ${customerCount}`);
      
      // Test enum values
      const testEnums = {
        CustomerStatus: ['ACTIVE', 'INACTIVE', 'PENDING_VERIFICATION', 'SUSPENDED'],
        CustomerType: ['INDIVIDUAL', 'BUSINESS', 'ENTERPRISE'],
        AddressType: ['HOME', 'WORK', 'BILLING', 'SHIPPING'],
        PaymentProvider: ['ELAVON', 'TSEP', 'NEARPAY', 'STRIPE'],
        PaymentTokenType: ['CARD', 'BANK_ACCOUNT', 'DIGITAL_WALLET'],
        PaymentTokenStatus: ['ACTIVE', 'EXPIRED', 'SUSPENDED', 'REVOKED'],
      };
      
      console.log('   ✅ Enum values validated');
      console.log('   ✅ Seeding prerequisites met');
      
    } catch (error) {
      console.error('   ❌ Seeding prerequisites check failed:', error.message);
      return false;
    }
    
    // 6. Performance test
    console.log('\n⚡ Performance Test:');
    try {
      const startTime = Date.now();
      
      // Create a small batch of test data
      const testData = [];
      for (let i = 0; i < 5; i++) {
        testData.push({
          firstName: `Test${i}`,
          lastName: 'Performance',
          email: `perf-test-${i}-${Date.now()}@validation.com`,
          phone: `******-${String(i).padStart(4, '0')}`,
          dateOfBirth: new Date('1990-01-01'),
          type: 'INDIVIDUAL',
          status: 'ACTIVE',
        });
      }
      
      // Batch create
      const created = await prisma.customer.createMany({
        data: testData,
      });
      
      // Clean up
      await prisma.customer.deleteMany({
        where: {
          email: {
            contains: 'perf-test-',
          },
        },
      });
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      console.log(`   ✅ Batch operations completed in ${duration}ms`);
      console.log(`   📈 Performance: ${(created.count / duration * 1000).toFixed(2)} records/second`);
      
    } catch (error) {
      console.error('   ❌ Performance test failed:', error.message);
      return false;
    }
    
    console.log('\n🎉 Database Configuration Validation PASSED!');
    console.log('✅ The seeding system is ready to work with your current database configuration.');
    return true;
    
  } catch (error) {
    console.error('\n❌ Validation failed with unexpected error:', error);
    return false;
  }
}

// Execute validation
validateDatabaseConfiguration()
  .then((success) => {
    if (success) {
      console.log('\n🚀 You can now run the seeding script with confidence!');
      console.log('   Command: docker-compose exec api node dist/prisma/production-seed.js');
      process.exit(0);
    } else {
      console.log('\n🔧 Please fix the issues above before running the seeding script.');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('❌ Fatal validation error:', error);
    process.exit(1);
  })
  .finally(async () => {
    try {
      await prisma.$disconnect();
    } catch (error) {
      console.error('Warning: Error disconnecting from database:', error.message);
    }
  });

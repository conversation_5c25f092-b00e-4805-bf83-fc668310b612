#!/bin/sh
echo "🌱 Running containerized database seeding..."
echo "⏳ Waiting for API container to be ready..."

# Wait for the API container to be fully started
until docker-compose exec api node -e "console.log('API Ready')" > /dev/null 2>&1; do
  echo "API container not ready - waiting..."
  sleep 2
done

echo "✅ API container is ready!"
echo "🚀 Starting production seeding..."

# Run the production seeding script
docker-compose exec api node dist/prisma/production-seed.js

echo "✅ Containerized seeding completed!"

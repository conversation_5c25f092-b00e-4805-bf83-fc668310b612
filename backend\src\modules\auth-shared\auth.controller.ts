import { <PERSON>, <PERSON>, Req, <PERSON><PERSON>, UnauthorizedEx<PERSON>, <PERSON>gger, HttpStatus, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiUnauthorizedResponse, ApiCookieAuth, ApiParam } from '@nestjs/swagger';
import { FastifyRequest, FastifyReply } from 'fastify';
import { AuthSharedService } from './auth.service';

@ApiTags('Authentication')
@Controller('auth')
export class AuthSharedController {
  private readonly logger = new Logger(AuthSharedController.name);

  constructor(private readonly authSharedService: AuthSharedService) {}

  /**
   * Parse cookie header manually for Fastify compatibility
   */
  private parseCookieHeader(cookieHeader: string): Record<string, string> {
    const cookies: Record<string, string> = {};

    cookieHeader.split(';').forEach(cookie => {
      const [name, ...rest] = cookie.trim().split('=');
      if (name && rest.length > 0) {
        const value = rest.join('='); // Handle values that contain '='
        cookies[name] = decodeURIComponent(value);
      }
    });

    return cookies;
  }







  @Get('me')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get current user information',
    description: 'Authenticates user using encrypted cookies, decrypts access token, verifies JWT with JWKS, and returns user information from external auth service'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User authenticated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        username: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        first_name: { type: 'string' },
        last_name: { type: 'string' },
        role: { type: 'string' },
        permissions: { type: 'array', items: { type: 'string' } },
        accounts: { type: 'array' },
        partners: { type: 'array' },
        active_accounts: { type: 'array' },
        active_partners: { type: 'array' },
        createdAt: { type: 'string' },
        updatedAt: { type: 'string' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed - invalid or missing cookies',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Authentication failed' }
      }
    }
  })
  async getCurrentUser(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ): Promise<any> {
    try {
      // Extract cookies from request - try multiple methods for Fastify
      let cookies = (request as any).cookies || {};

      this.logger.log(`🔐 [AUTH CONTROLLER] /auth/me endpoint called`);
      this.logger.log(`🔍 [AUTH CONTROLLER] Initial cookies object: ${JSON.stringify(cookies)}`);
      this.logger.log(`🔍 [AUTH CONTROLLER] Cookie header: ${request.headers.cookie || 'NOT FOUND'}`);

      // If no cookies found, try to parse from headers manually
      if (Object.keys(cookies).length === 0) {
        const cookieHeader = request.headers.cookie;
        if (cookieHeader) {
          this.logger.log(`🍪 [AUTH CONTROLLER] Parsing cookies from header: ${cookieHeader.substring(0, 100)}...`);
          cookies = this.parseCookieHeader(cookieHeader);
          this.logger.log(`🍪 [AUTH CONTROLLER] Parsed cookies: ${JSON.stringify(Object.keys(cookies))}`);
        } else {
          this.logger.log(`❌ [AUTH CONTROLLER] No cookie header found`);
        }
      }

      this.logger.log(`🍪 [AUTH CONTROLLER] Final cookies: ${JSON.stringify(Object.keys(cookies))}`);
      this.logger.log(`🔑 [AUTH CONTROLLER] Access token present: ${!!cookies.access_token}`);

      // Authenticate user using the new method that properly handles external auth service
      const user = await this.authSharedService.authenticateUserFromCookies(cookies);

      this.logger.log(`✅ [AUTH CONTROLLER] User authenticated successfully: ${user.email}`);

      return reply.status(HttpStatus.OK).send(user);
    } catch (error) {
      this.logger.error(`❌ [AUTH CONTROLLER] Authentication failed: ${error.message}`);
      throw new UnauthorizedException('Authentication failed');
    }
  }













  @Get('users/:id')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get user by ID',
    description: 'Retrieve user information by ID from external auth service using encrypted access token'
  })
  @ApiParam({
    name: 'id',
    description: 'User unique identifier (UUID)',
    type: 'string',
    format: 'uuid'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        email: { type: 'string' },
        first_name: { type: 'string' },
        last_name: { type: 'string' }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed or user not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Invalid or expired access token' }
      }
    }
  })
  async getUserById(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log(`Getting user by ID: ${id}`);

      // Get user from external auth service
      const user = await this.authSharedService.getUserById(id, cookies);

      return reply.status(HttpStatus.OK).send(user);
    } catch (error) {
      this.logger.error('Get user by ID failed:', error);
      throw new UnauthorizedException(error.message || 'Failed to retrieve user');
    }
  }

  @Get('users')
  @ApiCookieAuth('access_token')
  @ApiOperation({
    summary: 'Get all users',
    description: 'Retrieve all users from external auth service using encrypted access token. Automatically URL-decodes cookies before forwarding to external service.'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Users retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          email: { type: 'string' },
          first_name: { type: 'string' },
          last_name: { type: 'string' }
        }
      }
    }
  })
  @ApiUnauthorizedResponse({
    description: 'Authentication failed',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Invalid or expired access token' }
      }
    }
  })
  async getAllUsers(
    @Req() request: FastifyRequest,
    @Res() reply: FastifyReply
  ) {
    try {
      // Extract cookies from request
      const cookies = (request as any).cookies || {};

      this.logger.log('Getting all users');

      // Get users from external auth service
      const users = await this.authSharedService.getAllUsers(cookies);

      return reply.status(HttpStatus.OK).send(users);
    } catch (error) {
      this.logger.error('Get all users failed:', error);
      throw new UnauthorizedException(error.message || 'Failed to retrieve users');
    }
  }



}

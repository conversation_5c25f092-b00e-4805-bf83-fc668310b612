export interface User {
  id: string;
  email: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  role: string;
  permissions?: string[];
  createdAt: string;
  updatedAt: string;

  // Additional properties for compatibility with existing code
  phone?: string;
  password?: string;
  country?: string;
  verifiedEmail?: boolean;
  verifiedPhone?: boolean;
  totpSecret?: string;
  partnerId?: string;
  mfaEnabled?: boolean;
  active?: boolean;
  accountId?: string;
  isAdmin?: boolean;

  // External auth service fields
  accounts?: any[];
  partners?: any[];
  active_accounts?: any[];
  active_partners?: any[];
  first_name?: string;
  last_name?: string;
}

export interface JWTPayload {
  // Standard JWT claims
  iss: string; // Issuer (e.g., 'https://ngnair.com')
  sub: string; // Subject (User ID)
  aud: string | string[]; // Audience (e.g., ['partner'])
  exp: number; // Expires at (Unix timestamp)
  iat: number; // Issued at (Unix timestamp)
  jti: string; // JWT ID (unique identifier)

  // Custom claims
  email: string; // User email
  sid: string; // Session ID (e.g., "sess_a1b2c3d4")
  azp: string; // Authorized party (e.g., 'webapp')
  ent_set?: any; // Entity set
  perm_v?: any; // Permission version
  amr: string[]; // Authentication methods reference (e.g., ['pwd'])
  auth_time: number; // Authentication time (Unix timestamp)

  // Optional user information (for backward compatibility)
  username?: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  permissions?: string[];

  // External auth service fields
  accounts?: any[];
  partners?: any[];
  active_accounts?: any[];
  active_partners?: any[];
  first_name?: string;
  last_name?: string;
}

export interface AuthConfig {
  /**
   * AES-256 encryption key for decrypting Rails-compatible cookies
   * Must be a 64-character hexadecimal string (32 bytes)
   */
  encryptionKey: string;

  /**
   * JWKS URI for JWT verification (optional)
   * If not provided, JWT verification will be skipped (development mode)
   */
  authJwksUrl?: string;

  /**
   * External authentication service base URL
   * Used for /auth/users and /auth/users/{id} endpoints
   */
  externalAuthServiceUrl?: string;

  /**
   * Cookie names configuration
   */
  cookieNames: {
    accessToken: string;
    refreshToken: string;
  };

  /**
   * Request timeout for external API calls (in milliseconds)
   * Default: 10000 (10 seconds)
   */
  requestTimeout?: number;

  /**
   * Enable debug logging
   * Default: false
   */
  enableDebugLogging?: boolean;

  /**
   * JWT verification options
   */
  jwt?: {
    /**
     * JWT issuer to verify
     */
    issuer?: string;

    /**
     * JWT audience to verify
     */
    audience?: string;

    /**
     * Skip JWT verification (development only)
     * Default: false
     */
    skipVerification?: boolean;
  };
}

export interface DecryptedTokens {
  accessToken: string;
  refreshToken?: string;
}

export interface JWKSResponse {
  keys: Array<{
    kty: string;
    e: string;
    n: string;
    kid: string;
  }>;
}

/**
 * Cookie names configuration
 */
export interface CookieNames {
  accessToken: string;
  refreshToken: string;
}

/**
 * Authentication error response
 */
export interface AuthErrorResponse {
  success: false;
  error: string;
  message?: string;
}

/**
 * Authentication me response
 */
export interface AuthMeResponse {
  success: true;
  user: User;
}

/**
 * External user response from auth service
 */
export interface ExternalUserResponse {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  username?: string;
  role?: string;
  permissions?: string[];
  accounts?: any[];
  partners?: any[];
  active_accounts?: any[];
  active_partners?: any[];
}

/**
 * Default authentication configuration
 */
export const DEFAULT_AUTH_CONFIG: Partial<AuthConfig> = {
  cookieNames: {
    accessToken: 'access_token',
    refreshToken: 'refresh_token',
  },
  requestTimeout: 10000,
  enableDebugLogging: false,
  jwt: {
    skipVerification: false,
  },
};

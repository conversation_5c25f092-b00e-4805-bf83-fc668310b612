
#!/bin/sh

echo "🟡 Starting Prisma migration setup..."

# Parse a new URL with 'postgres' as the database name for admin connection
ADMIN_DB_URL=$(echo "$DATABASE_URL" | sed -E 's|/[^/]+$|/postgres|')

# Extract target DB name from DATABASE_URL
TARGET_DB=$(echo "$DATABASE_URL" | sed -E 's|.*/([^/?]+).*|\1|')

# Check if target database exists; create if it doesn't
echo "🧾 Checking if database '$TARGET_DB' exists..."
psql "$ADMIN_DB_URL" -tAc "SELECT 1 FROM pg_database WHERE datname = '$TARGET_DB'" | grep -q 1 || {
    echo "🆕 Database '$TARGET_DB' does not exist. Creating..."
    psql "$ADMIN_DB_URL" -c "CREATE DATABASE \"$TARGET_DB\""
}


# Check if the environment is staging or production
if [ "$NODE_ENV" = "staging" ]; then
    echo "🧪 Environment: STAGING"

    if [ -z "$(psql "$DATABASE_URL" -tAc "SELECT to_regclass('public._prisma_migrations')")" ]; then
        echo "🆕 First-time setup: running 'migrate dev --name init'"
        npx prisma migrate dev --name init --schema=./prisma/schema.prisma
    else
        echo "🔁 Database already initialized: running 'migrate dev'"
        npx prisma migrate dev --schema=./prisma/schema.prisma
    fi

else
    echo "🚀 Environment: PRODUCTION"
    echo "Running 'migrate deploy'"
    npx prisma migrate deploy --schema=./prisma/schema.prisma
fi

echo "✅ Migrations complete. Starting app..."

# Start your NestJS app (adjust as needed)
exec node dist/main.js
services:
  customer-embed:
    build:
      context: ./customer-embed
      dockerfile: Dockerfile
      args:
        NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY: b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
        NEXT_PUBLIC_AUTH_FRONTEND_URL: https://ng-auth-fe-dev.dev1.ngnair.com
        NEXT_PUBLIC_AUTH_JWKS_URL: https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
        NEXT_PUBLIC_CUSTOMER_API_URL: https://deployed-backend-url.com
        NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL: https://deployed-backend-url.com/graphql
        NEXT_PUBLIC_COOKIE_DOMAIN: .dev1.ngnair.com
        NEXT_PUBLIC_ALLOWED_ORIGINS: http://ng-customer-fe-local.dev.dev1.ngnair.com:3061,http://ng-customer-admin-local.dev.dev1.ngnair.com:3062
    container_name: customer-embed
    ports:
      - "3061:3061"
    environment:
      - NEXT_PUBLIC_CUSTOMER_API_URL=https://deployed-backend-url.com
      - NEXT_PUBLIC_BACKEND_URL=https://deployed-backend-url.com
      - NEXT_PUBLIC_AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
      - NEXT_PUBLIC_AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
      - NEXT_PUBLIC_ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
      - NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL=https://deployed-backend-url.com/graphql
      - NEXT_PUBLIC_COOKIE_DOMAIN=.dev1.ngnair.com
      - NEXT_PUBLIC_ALLOWED_ORIGINS=http://ng-customer-fe-local.dev.dev1.ngnair.com:3061,http://ng-customer-admin-local.dev.dev1.ngnair.com:3062
      - ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
      - PORT=3061
      - HOSTNAME=0.0.0.0
    networks:
      - customer-network

networks:
  customer-network:
    driver: bridge

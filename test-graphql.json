{"query": "mutation { decryptToken(input: { token: \"aVVoWEtGZkdDNTNoaEQ5WXE3SDd1bVgzYlJiOUpPNmQwdjBCUHhDQ1ZLUWpDYTNSTDZsS1FtN0V5aEFFVjZlRFRPem1Zc3hJZ1ZMaXV1ZWxyU3ZqZlF4Rm1lU243NVhHdkYwb1dHWDduMjRyTktWUGI0NERqM1B3MkNEWnF5NjhCTmcwSkZ1cTMxUXRnR1VKaXErb0ZSRDY4YkJua09OV0dkSmp1RnBuOTVzazlzMktUYlNvUm16SllBcElZak5SZ1R1RW1pRUFpbVpBVXI1QittOXgycTR1UExRK09jSWRFSWdQZ1Ura0pKUytoc1FjMlVoYnZ1MEhwMWV4cjBlY3d2NjVjOFVMMGRNQjZ1bHhFalRhYklqcDNET2ovK1V3VWREcnNDSGtNMzFiNnFwcmw5ckI4QnZGODE4VTkxQ2ZSMkM2b0xYbHl1QkV3YktIMFRHVWJjcjJoN281Z2w3QW81YllEVyswS2ViZUVxOVlqYytkNlZLVVpLMUxrVlM2N0IwYlVpMWpkWWdleDJObEZMcEdCQkxoK3h5NFZsbW50YUJqbzRJaWNKNzczMERvbklOcEZkWTAwQkZoL1p4QkRuOXJuMmg5WGE5ZHVtSkljd1dqTXhuLzRRazRFMXQ0Q1hRYkFYVVBLejhjWGEzQ0NYZTltbVVER1MrRGN3Vk44MzRIQXZJa3cxMGNJUExpK1lQak9XYlhzVGJ4TDBBek9hZDYxTWw5ZjJWNVZqSXR3bTRwajFNbkQ0L0E1R0JXUEhXaUlaZ2hONEhyTThScXNZekE3NkpmZ1lRWDBXbEo3Q0RMYUhDRTI4dDVsWnpaMFd2NzlmbjZVMW1lZDQ4V2w4amV6Mm4rS1dQT29WQmlkUmpsT3d5Nk1vcnhrTkhNVmlrQTNPSmgwRE9lcW9hUTNKdFVBT1dQa1NIT3AwbVJHTTROeG04OGFXc0JWUlpNTXhDRlBhamllT1laeTN4R2lOYytMOXRCQkZOQ3plZ2svaElVNnhaK0I4aXBNMGlveDVKOUdreUdESEM2VUtCd3dkMjVSL25mdmJnQWtCUzNObFpaUmJoVlgySTJDZ3B6YldCS1VPemxybm9ZWmN0U0xvYnJyODNhaVgwa01GSDdiTnd3bGh6MWdSeTVXeU5WOU1zNEJQVjB4aVVDQTFYK2dvMk5XQmRieGYwVDVBSVpOKyt3bThFdmNSM0o1OHl4dWNhcHNzLzE5a21CcEVOcGZ4M3RKUFBiOTlxV1ZsZkkwaTlQZXU5YnZUYXJsbzh1WXJBZEc1andWOXA2dU82RTk1Zjk4c2dyZWNDb2RoeU80NkthSkp0dTA1K04vME8zMTBMeWUvWjN0b1NqU05OdGRKSW42QnRoSmJkb01CZXNTUkgybkVxeFZ3VUZ6SnZIVE43SEhKNGxJOEtZS1VQUUM3ODhHcFRET25ZeHRGV1ZTdENPczRrK2xOL21aMWxPRnJ2NVhhdVpFSEhXcHRzNlQ5bW4rdz09LS14azRTcFdwa3lLVVpyTlZKLS1IQW1VeGJicFBNV2xOWUN0Uys3WE9nPT0%3D\" }) { success error decryptedToken tokenType } }"}